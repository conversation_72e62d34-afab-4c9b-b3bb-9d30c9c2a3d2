package my.com.mandrill.component.service.impl;

import jakarta.annotation.Nullable;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import my.com.mandrill.component.constant.NetWorthType;
import my.com.mandrill.component.domain.Bank;
import my.com.mandrill.component.domain.BankDetail;
import my.com.mandrill.component.domain.Loan;
import my.com.mandrill.component.service.BankLoanCalculationService;
import my.com.mandrill.component.service.BankService;
import my.com.mandrill.component.service.LoanService;
import my.com.mandrill.utilities.feign.dto.DetailedNetWorthDTO;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@RequiredArgsConstructor
@Service
public class BankLoanCalculationServiceImpl implements BankLoanCalculationService {

	private final LoanService loanService;

	private final BankService bankService;

	@Override
	public List<DetailedNetWorthDTO> calculateDetailedNetWorthByUserId(String userId) {
		List<DetailedNetWorthDTO> netWorthResponse = new ArrayList<>();
		List<Bank> bankList = bankService.findByUserId(userId, Sort.unsorted());
		List<Loan> loanList = loanService.findByUserId(userId);

		// build net worth response including assets, liabilities and recurring repayments
		netWorthResponse.addAll(calculateBankAssets(bankList));
		netWorthResponse.addAll(calculateLoanLiabilitiesAndRecurringRepayments(loanList));

		return netWorthResponse;
	}

	private List<DetailedNetWorthDTO> calculateBankAssets(List<Bank> bankList) {

		BigDecimal totalAssetBank = bankList.stream().map(this::calculateSingleBankAsset).reduce(BigDecimal.ZERO,
				BigDecimal::add);

		return List.of(DetailedNetWorthDTO.builder().netWorthType(NetWorthType.CASH_SAVINGS).value(totalAssetBank)
				.valueType(DetailedNetWorthDTO.ValueType.ASSET).build());
	}

	private BigDecimal calculateSingleBankAsset(Bank bank) {
		return bank.getBankDetails().stream().map(this::calculateBankDetailAsset).reduce(BigDecimal.ZERO,
				BigDecimal::add);
	}

	private BigDecimal calculateBankDetailAsset(BankDetail bankDetail) {
		BigDecimal assets = BigDecimal.ZERO;

		if (bankDetail.getSavingsAmount() != null) {
			assets = assets.add(bankDetail.getSavingsAmount());
		}
		if (bankDetail.getFixedDepositAmount() != null) {
			assets = assets.add(bankDetail.getFixedDepositAmount());
		}

		return assets;
	}

	private List<DetailedNetWorthDTO> calculateLoanLiabilitiesAndRecurringRepayments(List<Loan> loanList) {

		Map<NetWorthType, LoanSummary> loanSummaryByType = new HashMap<>();
		for (Loan loan : loanList) {
			NetWorthType netWorthType = getNetWorthType(loan);

			if (netWorthType == null) {
				continue;
			}

			BigDecimal endingBalance = loanService.calculateLoanEndingBalance(loan);
			DetailedNetWorthDTO.ValueFlag valueFlag = determineValueFlag(endingBalance);

			loanSummaryByType.merge(netWorthType,
					new LoanSummary(endingBalance, loan.getMonthlyInstallment(), valueFlag), this::mergeLoanSummaries);

		}

		List<DetailedNetWorthDTO> results = new ArrayList<>();

		for (Map.Entry<NetWorthType, LoanSummary> entry : loanSummaryByType.entrySet()) {

			// add liabilities
			results.add(buildDetailedNetWorthDTO(entry.getKey(), entry.getValue().totalOutstandingBalance(),
					DetailedNetWorthDTO.ValueType.LIABILITY, entry.getValue().valueFlag()));

			// add recurring repayments
			BigDecimal repaymentAmount = getRepaymentAmount(entry.getValue().totalMonthlyInstallment(),
					entry.getValue().valueFlag());
			results.add(buildDetailedNetWorthDTO(entry.getKey(), repaymentAmount,
					DetailedNetWorthDTO.ValueType.RECURRING_REPAYMENT, entry.getValue().valueFlag()));

		}

		return results;

	}

	private DetailedNetWorthDTO.ValueFlag determineValueFlag(BigDecimal endingBalance) {

		return endingBalance.compareTo(BigDecimal.ZERO) <= 0 ? DetailedNetWorthDTO.ValueFlag.FULLY_PAID
				: DetailedNetWorthDTO.ValueFlag.ON_GOING;

	}

	private LoanSummary mergeLoanSummaries(LoanSummary existing, LoanSummary incoming) {
		BigDecimal combinedOutstandingBalance = existing.totalOutstandingBalance()
				.add(incoming.totalOutstandingBalance());
		BigDecimal combinedMonthlyInstallment = existing.totalMonthlyInstallment()
				.add(incoming.totalMonthlyInstallment());

		// mark as ongoing if any of the loan is ongoing
		DetailedNetWorthDTO.ValueFlag combinedValueFlag = DetailedNetWorthDTO.ValueFlag.ON_GOING
				.equals(existing.valueFlag()) || DetailedNetWorthDTO.ValueFlag.ON_GOING.equals(incoming.valueFlag())
						? DetailedNetWorthDTO.ValueFlag.ON_GOING : DetailedNetWorthDTO.ValueFlag.FULLY_PAID;

		return new LoanSummary(combinedOutstandingBalance, combinedMonthlyInstallment, combinedValueFlag);

	}

	private BigDecimal getRepaymentAmount(BigDecimal monthlyInstallment, DetailedNetWorthDTO.ValueFlag valueFlag) {
		// set repayment amount to 0 if loan is fully paid
		return DetailedNetWorthDTO.ValueFlag.FULLY_PAID.equals(valueFlag) ? BigDecimal.ZERO : monthlyInstallment;
	}

	private DetailedNetWorthDTO buildDetailedNetWorthDTO(NetWorthType netWorthType, BigDecimal value,
			DetailedNetWorthDTO.ValueType valueType, DetailedNetWorthDTO.ValueFlag valueFlag) {
		return DetailedNetWorthDTO.builder().netWorthType(netWorthType).value(value).valueType(valueType)
				.valueFlag(valueFlag).build();
	}

	@Nullable
	private NetWorthType getNetWorthType(Loan loan) {

		return switch (loan.getType()) {
			case AUTO_LOANS -> NetWorthType.VEHICLE_LOAN_MONTHLY_REPAYMENT;
			case EDUCATION_LOANS -> NetWorthType.EDUCATION_LOAN_MONTHLY_REPAYMENT;
			case HOME_LOANS -> NetWorthType.MORTGAGE_LOAN_MONTHLY_REPAYMENT;
			case OTHER_LOAN -> NetWorthType.OTHER_LOAN_MONTHLY_REPAYMENT;
			case PERSONAL_LOANS -> NetWorthType.PERSONAL_LOAN_MONTHLY_REPAYMENT;
			case ISLAMIC_FINANCING -> NetWorthType.ISLAMIC_FINANCING_LOAN_MONTHLY_REPAYMENT;
			default -> null;
		};
	}

	private record LoanSummary(BigDecimal totalOutstandingBalance, BigDecimal totalMonthlyInstallment,
			DetailedNetWorthDTO.ValueFlag valueFlag) {

	}

}
