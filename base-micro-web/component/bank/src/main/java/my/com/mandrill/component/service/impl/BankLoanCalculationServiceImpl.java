package my.com.mandrill.component.service.impl;

import jakarta.annotation.Nullable;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import my.com.mandrill.component.constant.NetWorthType;
import my.com.mandrill.component.domain.Bank;
import my.com.mandrill.component.domain.BankDetail;
import my.com.mandrill.component.domain.Loan;
import my.com.mandrill.component.service.BankLoanCalculationService;
import my.com.mandrill.component.service.BankService;
import my.com.mandrill.component.service.LoanService;
import my.com.mandrill.utilities.feign.dto.DetailedNetWorthDTO;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;

@Slf4j
@RequiredArgsConstructor
@Service
public class BankLoanCalculationServiceImpl implements BankLoanCalculationService {

	private final LoanService loanService;

	private final BankService bankService;

	@Override
	public List<DetailedNetWorthDTO> calculateDetailedNetWorthByUserId(String userId) {
		List<DetailedNetWorthDTO> netWorthResponse = new ArrayList<>();
		List<Bank> bankList = bankService.findByUserId(userId, Sort.unsorted());
		List<Loan> loanList = loanService.findByUserId(userId);

		netWorthResponse.addAll(calculateBankAssets(bankList));
		netWorthResponse.addAll(calculateBankLiabilities(loanList));
		netWorthResponse.addAll(calculateBankRecurringRepayments(loanList));

		return netWorthResponse;
	}

	private List<DetailedNetWorthDTO> calculateBankAssets(List<Bank> bankList) {

		BigDecimal totalAssetBank = bankList.stream().map(this::calculateAsset).reduce(BigDecimal.ZERO,
				BigDecimal::add);

		return List.of(DetailedNetWorthDTO.builder().netWorthType(NetWorthType.CASH_SAVINGS).value(totalAssetBank)
				.valueType(DetailedNetWorthDTO.ValueType.ASSET).build());
	}

	private List<DetailedNetWorthDTO> calculateBankLiabilities(List<Loan> loanList) {

		Map<NetWorthType, BigDecimal> totalOutstandingBalanceByLoanType = new HashMap<>();
		Map<NetWorthType, DetailedNetWorthDTO.ValueFlag> loanValueFlag = new HashMap<>();

		for (Loan loan : loanList) {
			NetWorthType netWorthType = getNetWorthType(loan);
			if (netWorthType != null) {

				BigDecimal endingBalance = loanService.calculateLoanEndingBalance(loan);
				loanValueFlag.compute(netWorthType, (k, v) -> {
					if (Objects.isNull(v) && BigDecimal.ZERO.compareTo(endingBalance) >= 0) {
						return DetailedNetWorthDTO.ValueFlag.FULLY_PAID;
					}
					else if (BigDecimal.ZERO.compareTo(endingBalance) < 0) {
						return DetailedNetWorthDTO.ValueFlag.ON_GOING;
					}
					return v;
				});
				totalOutstandingBalanceByLoanType.merge(netWorthType, endingBalance, BigDecimal::add);
			}
		}

		return totalOutstandingBalanceByLoanType.entrySet().stream()
				.map(v -> DetailedNetWorthDTO.builder().netWorthType(v.getKey()).value(v.getValue())
						.valueType(DetailedNetWorthDTO.ValueType.LIABILITY).valueFlag(loanValueFlag.get(v.getKey()))
						.build())
				.toList();
	}

	private List<DetailedNetWorthDTO> calculateBankRecurringRepayments(List<Loan> loanList) {

		Map<NetWorthType, BigDecimal> totalMonthlyInstallmentByLoanType = new HashMap<>();
		Map<NetWorthType, DetailedNetWorthDTO.ValueFlag> loanValueFlag = new HashMap<>();

		for (Loan loan : loanList) {
			NetWorthType netWorthType = getNetWorthType(loan);
			if (netWorthType != null) {
				totalMonthlyInstallmentByLoanType.merge(netWorthType, loan.getMonthlyInstallment(), BigDecimal::add);

				BigDecimal endingBalance = loanService.calculateLoanEndingBalance(loan);
				loanValueFlag.compute(netWorthType, (k, v) -> {
					if (Objects.isNull(v) && BigDecimal.ZERO.compareTo(endingBalance) >= 0) {
						return DetailedNetWorthDTO.ValueFlag.FULLY_PAID;
					}
					else if (BigDecimal.ZERO.compareTo(endingBalance) < 0) {
						return DetailedNetWorthDTO.ValueFlag.ON_GOING;
					}
					return v;
				});
			}
		}

		return totalMonthlyInstallmentByLoanType.entrySet().stream()
				.map(v -> DetailedNetWorthDTO.builder().netWorthType(v.getKey())
						.value(DetailedNetWorthDTO.ValueFlag.FULLY_PAID.equals(loanValueFlag.get(v.getKey()))
								? BigDecimal.ZERO : v.getValue())
						.valueType(DetailedNetWorthDTO.ValueType.RECURRING_REPAYMENT)
						.valueFlag(loanValueFlag.get(v.getKey())).build())
				.toList();

	}

	@Nullable
	private NetWorthType getNetWorthType(Loan loan) {
		NetWorthType netWorthType;

		switch (loan.getType()) {
			case AUTO_LOANS -> netWorthType = NetWorthType.VEHICLE_LOAN_MONTHLY_REPAYMENT;
			case EDUCATION_LOANS -> netWorthType = NetWorthType.EDUCATION_LOAN_MONTHLY_REPAYMENT;
			case HOME_LOANS -> netWorthType = NetWorthType.MORTGAGE_LOAN_MONTHLY_REPAYMENT;
			case OTHER_LOAN -> netWorthType = NetWorthType.OTHER_LOAN_MONTHLY_REPAYMENT;
			case PERSONAL_LOANS -> netWorthType = NetWorthType.PERSONAL_LOAN_MONTHLY_REPAYMENT;
			case ISLAMIC_FINANCING -> netWorthType = NetWorthType.ISLAMIC_FINANCING_LOAN_MONTHLY_REPAYMENT;
			default -> netWorthType = null;
		}
		return netWorthType;
	}

	private BigDecimal calculateAsset(Bank bank) {
		BigDecimal assets = new BigDecimal(0);
		for (BankDetail bankDetail : bank.getBankDetails()) {
			if (bankDetail.getSavingsAmount() != null) {
				BigDecimal savingAmount = bankDetail.getSavingsAmount();
				assets = assets.add(savingAmount);
			}
			if (bankDetail.getFixedDepositAmount() != null) {
				BigDecimal fixedDepositAmount = bankDetail.getFixedDepositAmount();
				assets = assets.add(fixedDepositAmount);
			}
		}
		return assets;
	}

}
