package my.com.mandrill.component.service.impl;

import jakarta.annotation.Nullable;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import my.com.mandrill.component.constant.NetWorthType;
import my.com.mandrill.component.domain.Bank;
import my.com.mandrill.component.domain.BankDetail;
import my.com.mandrill.component.domain.Loan;
import my.com.mandrill.component.service.BankLoanCalculationService;
import my.com.mandrill.component.service.BankService;
import my.com.mandrill.component.service.LoanService;
import my.com.mandrill.utilities.feign.dto.DetailedNetWorthDTO;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@RequiredArgsConstructor
@Service
public class BankLoanCalculationServiceImpl implements BankLoanCalculationService {

	private final LoanService loanService;

	private final BankService bankService;

	@Override
	public List<DetailedNetWorthDTO> calculateDetailedNetWorthByUserId(String userId) {
		List<DetailedNetWorthDTO> netWorthResponse = new ArrayList<>();
		List<Bank> bankList = bankService.findByUserId(userId, Sort.unsorted());
		List<Loan> loanList = loanService.findByUserId(userId);

		netWorthResponse.addAll(calculateBankAssets(bankList));
		netWorthResponse.addAll(calculateLoanLiabilities(loanList));
		netWorthResponse.addAll(calculateRecurringLoanRepayments(loanList));

		return netWorthResponse;
	}

	private List<DetailedNetWorthDTO> calculateBankAssets(List<Bank> bankList) {

		BigDecimal totalAssetBank = bankList.stream().map(this::calculateSingleBankAsset).reduce(BigDecimal.ZERO,
				BigDecimal::add);

		return List.of(DetailedNetWorthDTO.builder().netWorthType(NetWorthType.CASH_SAVINGS).value(totalAssetBank)
				.valueType(DetailedNetWorthDTO.ValueType.ASSET).build());
	}

	private BigDecimal calculateSingleBankAsset(Bank bank) {
		return bank.getBankDetails().stream().map(this::calculateBankDetailAsset).reduce(BigDecimal.ZERO,
				BigDecimal::add);
	}

	private BigDecimal calculateBankDetailAsset(BankDetail bankDetail) {
		BigDecimal assets = BigDecimal.ZERO;

		if (bankDetail.getSavingsAmount() != null) {
			assets = assets.add(bankDetail.getSavingsAmount());
		}
		if (bankDetail.getFixedDepositAmount() != null) {
			assets = assets.add(bankDetail.getFixedDepositAmount());
		}

		return assets;
	}

	private List<DetailedNetWorthDTO> calculateLoanLiabilities(List<Loan> loanList) {

		Map<NetWorthType, LoanSummary> loanSummaryByType = new HashMap<>();
		for (Loan loan : loanList) {
			NetWorthType netWorthType = getNetWorthType(loan);

			if (netWorthType != null) {
				BigDecimal endingBalance = loanService.calculateLoanEndingBalance(loan);
				DetailedNetWorthDTO.ValueFlag valueFlag = determineValueFlag(endingBalance);

				loanSummaryByType.merge(netWorthType, new LoanSummary(endingBalance, valueFlag),
						this::mergeLoanSummaries);

			}
		}

		return loanSummaryByType.entrySet().stream()
				.map(entry -> DetailedNetWorthDTO.builder().netWorthType(entry.getKey())
						.value(entry.getValue().totalBalance()).valueType(DetailedNetWorthDTO.ValueType.LIABILITY)
						.valueFlag(entry.getValue().valueFlag()).build())
				.toList();

	}

	private List<DetailedNetWorthDTO> calculateRecurringLoanRepayments(List<Loan> loanList) {

		Map<NetWorthType, LoanSummary> repaymentSummaryByType = new HashMap<>();

		for (Loan loan : loanList) {
			NetWorthType netWorthType = getNetWorthType(loan);
			if (netWorthType != null) {

				BigDecimal endingBalance = loanService.calculateLoanEndingBalance(loan);
				DetailedNetWorthDTO.ValueFlag valueFlag = determineValueFlag(endingBalance);

				repaymentSummaryByType.merge(netWorthType, new LoanSummary(loan.getMonthlyInstallment(), valueFlag),
						this::mergeLoanSummaries);
			}
		}

		return repaymentSummaryByType.entrySet().stream().map(entry -> {

			BigDecimal repaymentAmount = DetailedNetWorthDTO.ValueFlag.FULLY_PAID.equals(entry.getValue().valueFlag())
					? BigDecimal.ZERO : entry.getValue().totalBalance();

			return DetailedNetWorthDTO.builder().netWorthType(entry.getKey()).value(repaymentAmount)
					.valueType(DetailedNetWorthDTO.ValueType.RECURRING_REPAYMENT)
					.valueFlag(entry.getValue().valueFlag()).build();
		}).toList();

	}

	private DetailedNetWorthDTO.ValueFlag determineValueFlag(BigDecimal endingBalance) {

		return endingBalance.compareTo(BigDecimal.ZERO) <= 0 ? DetailedNetWorthDTO.ValueFlag.FULLY_PAID
				: DetailedNetWorthDTO.ValueFlag.ON_GOING;

	}

	private LoanSummary mergeLoanSummaries(LoanSummary existing, LoanSummary incoming) {
		BigDecimal combinedBalance = existing.totalBalance.add(incoming.totalBalance);

		// mark as ongoing if any of the loan is ongoing
		DetailedNetWorthDTO.ValueFlag combinedValueFlag = DetailedNetWorthDTO.ValueFlag.ON_GOING
				.equals(existing.valueFlag()) || DetailedNetWorthDTO.ValueFlag.ON_GOING.equals(incoming.valueFlag())
						? DetailedNetWorthDTO.ValueFlag.ON_GOING : DetailedNetWorthDTO.ValueFlag.FULLY_PAID;

		return new LoanSummary(combinedBalance, combinedValueFlag);

	}

	@Nullable
	private NetWorthType getNetWorthType(Loan loan) {

		return switch (loan.getType()) {
			case AUTO_LOANS -> NetWorthType.VEHICLE_LOAN_MONTHLY_REPAYMENT;
			case EDUCATION_LOANS -> NetWorthType.EDUCATION_LOAN_MONTHLY_REPAYMENT;
			case HOME_LOANS -> NetWorthType.MORTGAGE_LOAN_MONTHLY_REPAYMENT;
			case OTHER_LOAN -> NetWorthType.OTHER_LOAN_MONTHLY_REPAYMENT;
			case PERSONAL_LOANS -> NetWorthType.PERSONAL_LOAN_MONTHLY_REPAYMENT;
			case ISLAMIC_FINANCING -> NetWorthType.ISLAMIC_FINANCING_LOAN_MONTHLY_REPAYMENT;
			default -> null;
		};
	}

	private record LoanSummary(BigDecimal totalBalance, DetailedNetWorthDTO.ValueFlag valueFlag) {
	}

}
