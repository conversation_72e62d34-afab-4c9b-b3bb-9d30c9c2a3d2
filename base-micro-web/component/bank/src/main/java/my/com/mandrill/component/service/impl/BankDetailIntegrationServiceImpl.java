package my.com.mandrill.component.service.impl;

import jakarta.persistence.EntityNotFoundException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import my.com.mandrill.component.config.MapStructConverter;
import my.com.mandrill.component.constant.NetWorthMainType;
import my.com.mandrill.component.constant.NetWorthSource;
import my.com.mandrill.component.constant.NetWorthType;
import my.com.mandrill.component.domain.Bank;
import my.com.mandrill.component.domain.BankDetail;
import my.com.mandrill.component.dto.model.BankDTO;
import my.com.mandrill.component.dto.request.BankDetailCreateRequest;
import my.com.mandrill.component.dto.request.BankDetailUpdateRequest;
import my.com.mandrill.component.dto.request.ReminderIntegrationRequest;
import my.com.mandrill.component.dto.response.BankDetailResponse;
import my.com.mandrill.component.exception.ErrorCodeEnum;
import my.com.mandrill.component.service.*;
import my.com.mandrill.utilities.feign.client.NotificationFeignClient;
import my.com.mandrill.utilities.feign.dto.DetailedNetWorthDTO;
import my.com.mandrill.utilities.feign.dto.ObjectRequest;
import my.com.mandrill.utilities.feign.dto.ReminderRequest;
import my.com.mandrill.utilities.feign.dto.ReminderResponse;
import my.com.mandrill.utilities.feign.dto.request.UpdateUserNetWorthTransactionRequest;
import my.com.mandrill.utilities.general.constant.*;
import my.com.mandrill.utilities.general.exception.BusinessException;
import my.com.mandrill.utilities.general.service.KafkaSender;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.Year;
import java.util.LinkedList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

@Slf4j
@RequiredArgsConstructor
@Service
public class BankDetailIntegrationServiceImpl implements BankDetailIntegrationService {

	private final NotificationFeignClient notificationFeignClient;

	private final CreditCardTypeService creditCardTypeService;

	private final BankDetailService bankDetailService;

	private final BankService bankService;

	private final BankIntegrationService bankIntegrationService;

	private final BankListService bankListService;

	private final NotificationIntegrationService notificationIntegrationService;

	private final BankLoanCalculationService bankLoanCalculationService;

	private final KafkaSender kafkaSender;

	@Override
	public BankDTO processReminderResponse(BankDTO bankDTO) {
		bankDTO.getBankDetails().forEach(bankDetailDTO -> {
			if (Boolean.TRUE.equals(bankDetailDTO.getIsReminder())) {
				try {
					ReminderResponse reminderResponse = notificationFeignClient
							.findByReminderTypeAndDataId(ReminderType.BANK, bankDetailDTO.getId());
					bankDetailDTO.setReminder(reminderResponse);
				}
				catch (EntityNotFoundException e) {
					bankDetailDTO.setIsReminder(false);
					bankDetailDTO.setReminder(null);
				}
			}
		});
		return bankDTO;
	}

	@Override
	public BankDetailResponse findAndSetReminderResponse(BankDetailResponse bankDetailResponse) {
		try {
			ReminderResponse reminderResponse = notificationFeignClient.findByReminderTypeAndDataId(ReminderType.BANK,
					bankDetailResponse.getId());
			bankDetailResponse.setReminder(reminderResponse);
		}
		catch (EntityNotFoundException e) {
			bankDetailResponse.setIsReminder(false);
			bankDetailResponse.setReminder(null);
		}
		return bankDetailResponse;
	}

	@Override
	public BankDetail processUpdate(BankDetail existingBankDetail, BankDetailUpdateRequest bankDetailUpdateRequest) {
		this.validationForYearInBank(bankDetailUpdateRequest.getBankCardExpirationYear(),
				bankDetailUpdateRequest.getFixedDepositMaturityYear(),
				bankDetailUpdateRequest.getCreditCardTypeExpiryYear());

		existingBankDetail.setLabel(bankDetailUpdateRequest.getLabel());
		existingBankDetail.setSavingsAmount(bankDetailUpdateRequest.getSavingsAmount());
		existingBankDetail.setBankCardExpirationMonth(bankDetailUpdateRequest.getBankCardExpirationMonth());
		existingBankDetail.setBankCardExpirationYear(bankDetailUpdateRequest.getBankCardExpirationYear());
		if (Objects.nonNull(bankDetailUpdateRequest.getCreditCardType())) {
			existingBankDetail.setCreditCardType(
					creditCardTypeService.findById(bankDetailUpdateRequest.getCreditCardType().getId()));
		}
		existingBankDetail.setFixedDepositAmount(bankDetailUpdateRequest.getFixedDepositAmount());
		existingBankDetail.setCreditCardTypeExpiryMonth(bankDetailUpdateRequest.getCreditCardTypeExpiryMonth());
		existingBankDetail.setCreditCardTypeExpiryYear(bankDetailUpdateRequest.getCreditCardTypeExpiryYear());
		existingBankDetail.setCardProduct(bankDetailUpdateRequest.getCardProduct());
		existingBankDetail.setCreditCardLimit(bankDetailUpdateRequest.getCreditCardLimit());
		existingBankDetail.setTenure(bankDetailUpdateRequest.getTenure());
		existingBankDetail.setFixedDepositMaturityMonth(bankDetailUpdateRequest.getFixedDepositMaturityMonth());
		existingBankDetail.setFixedDepositMaturityYear(bankDetailUpdateRequest.getFixedDepositMaturityYear());
		existingBankDetail.setFixedDepositStartMonth(bankDetailUpdateRequest.getFixedDepositStartMonth());
		existingBankDetail.setFixedDepositStartYear(bankDetailUpdateRequest.getFixedDepositStartYear());
		existingBankDetail.setFixedDepositInterestRate(bankDetailUpdateRequest.getFixedDepositInterestRate());
		existingBankDetail.setIsReminder(bankDetailUpdateRequest.getIsReminder());
		existingBankDetail.setAttachmentGroupId(bankDetailUpdateRequest.getAdvertisementId());
		existingBankDetail.setMonthlyPaymentDueDate(bankDetailUpdateRequest.getMonthlyPaymentDueDate());
		existingBankDetail = bankDetailService.save(existingBankDetail);

		if (AccountType.CREDIT_CARD.equals(existingBankDetail.getAccountType())) {
			this.saveCombineCreditCardLimit(existingBankDetail, bankDetailUpdateRequest);
		}

		notificationIntegrationService.bankDetailUpdatedEvent(existingBankDetail);
		if (Boolean.TRUE.equals(existingBankDetail.getIsReminder())) {
			if (Objects.nonNull(bankDetailUpdateRequest.getReminderIntegrationRequest())) {
				this.createOrUpdateReminder(existingBankDetail,
						bankDetailUpdateRequest.getReminderIntegrationRequest());
			}
		}
		else {
			try {
				this.deleteReminder(existingBankDetail.getId());
			}
			catch (EntityNotFoundException e) {
				return existingBankDetail;
			}
		}

		return existingBankDetail;
	}

	@Override
	public List<BankDetail> processCreate(String userId, List<BankDetailCreateRequest> bankDetailCreateRequest) {
		log.info("process create bank-detail: {} data", bankDetailCreateRequest.size());
		List<BankDetail> bankDetails = new LinkedList<>();
		for (BankDetailCreateRequest bankDetail : bankDetailCreateRequest) {
			log.info("item iteration create bank-detail start");
			bankDetails.add(processCreate(userId, bankDetail));

			log.info("item iteration create bank-detail success");
		}
		log.info("create bank-detail success all data saved");
		return bankDetails;
	}

	@Override
	public BankDetail processCreate(String userId, BankDetailCreateRequest bankDetailCreateRequest) {
		this.validationForYearInBank(bankDetailCreateRequest.getBankCardExpirationYear(),
				bankDetailCreateRequest.getFixedDepositMaturityYear(),
				bankDetailCreateRequest.getCreditCardTypeExpiryYear());

		// see if the bank is created before or not
		Optional<Bank> bankOptional = bankService
				.findByBankListIdAndUserId(bankDetailCreateRequest.getBankList().getId(), userId);
		Bank bank;
		if (bankOptional.isPresent()) {
			bank = bankOptional.get();
			if (AccountType.CREDIT_CARD.equals(bankDetailCreateRequest.getAccountType())) {
				bank.setCombineCreditCardLimit(bankDetailCreateRequest.getCreditCardLimit());
			}
		}
		else {
			bank = new Bank();
			bank.setBankList(bankListService.findById(bankDetailCreateRequest.getBankList().getId()));
			if (AccountType.CREDIT_CARD.equals(bankDetailCreateRequest.getAccountType())) {
				bank.setCombineCreditCardLimit(bankDetailCreateRequest.getCreditCardLimit());
			}
			bank.setUserId(userId);
		}
		Bank persistBank = bankService.save(bank);

		// then save bank detail
		BankDetail bankDetail = MapStructConverter.MAPPER.toBankDetail(bankDetailCreateRequest);
		bankDetail.setBank(persistBank);
		BankDetail persistedBankDetail = bankDetailService.save(bankDetail);

		// dashboard activity
		bankIntegrationService.sendDashboardActivity(bankDetail.getAccountType(), bank.getCreatedDate());

		// complete uj if have
		if (Objects.nonNull(bankDetailCreateRequest.getUserJourneyId())) {
			bankIntegrationService.completeUserJourney(bank.getId(), bankDetailCreateRequest.getUserJourneyId());
		}
		notificationIntegrationService.bankDetailCreatedEvent(bankDetail);

		return persistedBankDetail;
	}

	private void validationForYearInBank(String bankCardExpirationYear, String fixedDepositMaturityYear,
			String creditCardTypeExpiryYear) {
		if (Objects.nonNull(bankCardExpirationYear)) {
			validationForYear(bankCardExpirationYear);
		}
		else if (Objects.nonNull(fixedDepositMaturityYear)) {
			validationForYear(fixedDepositMaturityYear);
		}
		else if (Objects.nonNull(creditCardTypeExpiryYear)) {
			validationForYear(creditCardTypeExpiryYear);
		}
	}

	private void validationForYear(String yearString) {
		int yearInt;
		yearInt = Integer.parseInt(yearString);

		Year currentYear = Year.now();
		Year maxYear = currentYear.plusYears(
				Integer.parseInt(SystemConfigurationEnum.DEFAULT_YEAR_RANGE_OF_EXPIRY_CARD_PICKLIST.getValue()));

		if (yearInt < currentYear.getValue() || yearInt > maxYear.getValue()) {
			throw new BusinessException(ErrorCodeEnum.INVALID_YEAR);
		}

	}

	private void createOrUpdateReminder(BankDetail bankDetail, ReminderIntegrationRequest reminderIntegrationRequest) {
		ObjectRequest data = new ObjectRequest();
		data.setId(bankDetail.getId());
		ReminderRequest reminderRequest = getReminderRequest(reminderIntegrationRequest, data);
		ReminderResponse response;
		try {
			response = notificationFeignClient.findByReminderTypeAndDataId(reminderRequest.getReminderType(),
					reminderRequest.getData().getId());
			notificationFeignClient.update(reminderRequest, response.getId());
		}
		catch (EntityNotFoundException e) {
			notificationFeignClient.integration(reminderRequest);
		}
	}

	private ReminderRequest getReminderRequest(ReminderIntegrationRequest reminderIntegrationRequest,
			ObjectRequest data) {
		ReminderRequest reminderRequest = new ReminderRequest();
		reminderRequest.setReminderType(ReminderType.BANK);
		reminderRequest.setStartDate(reminderIntegrationRequest.getStartDate());
		reminderRequest.setDeliveryType(DeliveryType.PUSH);
		reminderRequest.setReminderFrequency(ReminderFrequency.ONCE);
		reminderRequest.setData(data);
		reminderRequest.setTitle(reminderIntegrationRequest.getTitle());
		reminderRequest.setNote(reminderIntegrationRequest.getNote());
		reminderRequest.setIsRevampVersion(reminderIntegrationRequest.getIsRevampVersion());
		return reminderRequest;
	}

	private void createReminder(BankDetail bankDetail, ReminderIntegrationRequest reminderIntegrationRequest) {
		ObjectRequest data = new ObjectRequest();
		data.setId(bankDetail.getId());
		ReminderRequest reminderRequest = getReminderRequest(reminderIntegrationRequest, data);
		notificationFeignClient.integration(reminderRequest);
	}

	private void deleteReminder(String id) {
		notificationFeignClient.deleteByReminderTypeAndDataId(ReminderType.BANK, id);
	}

	private void saveCombineCreditCardLimit(BankDetail bankDetail, BankDetailUpdateRequest bankDetailUpdateRequest) {
		if (Objects.nonNull(bankDetailUpdateRequest.getCreditCardLimit())) {
			Bank bank = bankService.findById(bankDetail.getBank().getId());
			if (!bank.getCombineCreditCardLimit().equals(bankDetailUpdateRequest.getCreditCardLimit())) {
				bank.setCombineCreditCardLimit(bankDetailUpdateRequest.getCreditCardLimit());
				bankService.save(bank);
			}
		}
	}

	@Override
	public void processDelete(String id, String userId) {
		bankDetailService.findOptionalByIdAndBankUserId(id, userId).ifPresent(bankDetail -> {
			Bank bank = bankDetail.getBank();

			bankService.deleteBankDetail(bankDetail);

			bankService.deleteByIdAndUserIdAndBankDetailsEmptyAndLoansEmpty(bank.getId(), userId);
			bankIntegrationService.deleteBankReminder(bankDetail.getId());
			notificationIntegrationService.bankDetailDeletedEvent(bankDetail);

			bankIntegrationService.deleteSavingGoalAccount(bankDetail);
		});
	}

	@Override
	public void publishNetWorthTransactionEvent(String userId, NetWorthMainType mainType, NetWorthType subType,
			NetWorthSource source) {

		BigDecimal totalCashSaving = bankLoanCalculationService.calculateDetailedNetWorthByUserId(userId).stream()
				.filter(data -> DetailedNetWorthDTO.ValueType.ASSET.equals(data.getValueType()))
				.map(DetailedNetWorthDTO::getValue).reduce(BigDecimal.ZERO, BigDecimal::add);

		UpdateUserNetWorthTransactionRequest request = UpdateUserNetWorthTransactionRequest.builder().userId(userId)
				.mainType(mainType).subType(subType).amount(totalCashSaving).source(source).build();

		kafkaSender.safeSendWrapped(KafkaTopic.UPDATE_USER_NET_WORTH_TRANSACTION_TOPIC, userId, request);

	}

}
