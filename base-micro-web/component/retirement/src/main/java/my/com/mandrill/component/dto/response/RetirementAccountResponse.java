package my.com.mandrill.component.dto.response;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import my.com.mandrill.component.dto.model.RetirementAccountTypeDTO;
import my.com.mandrill.component.dto.model.RetirementProviderDTO;

import java.math.BigDecimal;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class RetirementAccountResponse {

	private String id;

	private RetirementAccountTypeDTO accountType;

	private RetirementProviderDTO retirementProvider;

	private BigDecimal savingsAmount;

	private BigDecimal monthlyIncome;

	private BigDecimal employeeContributionPercentage;

	private BigDecimal employerContributionPercentage;

	private BigDecimal monthlyContribution;

	private String attachmentGroupId;

}
