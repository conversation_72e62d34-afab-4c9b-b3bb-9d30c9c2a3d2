package my.com.mandrill.component.dto.request;

import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import my.com.mandrill.utilities.general.constant.ApplicationType;

import java.time.Instant;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class AdminCreateManualLeadRequest {

	@NotNull
	private ApplicationType applicationType;

	private String userId;

	private String userRefNo;

	private String productCategory;

	private String productType;

	private String providerId;

	private String productName;

	private String productId;

	private String remarks;

	private Instant applicationDate;

}
