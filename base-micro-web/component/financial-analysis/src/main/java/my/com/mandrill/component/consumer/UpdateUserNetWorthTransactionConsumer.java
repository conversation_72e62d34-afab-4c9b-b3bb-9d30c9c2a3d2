package my.com.mandrill.component.consumer;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import my.com.mandrill.component.config.KafkaTopicConfig;
import my.com.mandrill.component.service.UserNetWorthTransactionIntgService;
import my.com.mandrill.utilities.feign.dto.request.UpdateUserNetWorthTransactionRequest;
import my.com.mandrill.utilities.general.constant.KafkaTopic;
import my.com.mandrill.utilities.general.dto.kafka.KafkaEventWrapper;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.time.Instant;

@Slf4j
@Service
@RequiredArgsConstructor
public class UpdateUserNetWorthTransactionConsumer {

	private final ObjectMapper objectMapper;

	private final UserNetWorthTransactionIntgService userNetWorthTransactionIntgService;

	@KafkaListener(topics = KafkaTopic.UPDATE_USER_NET_WORTH_TRANSACTION_TOPIC, groupId = KafkaTopicConfig.GROUP,
			id = KafkaTopic.UPDATE_USER_NET_WORTH_TRANSACTION_TOPIC)
	public void consume(String message) throws JsonProcessingException {

		try {
			Instant now = Instant.now();
			KafkaEventWrapper<UpdateUserNetWorthTransactionRequest> wrappedEvent = objectMapper.readValue(message,
					new TypeReference<>() {
					});

			log.info("Update User Net Worth Transaction Begin, requestId: {}, message: {}", wrappedEvent.getRequestId(),
					message);

			userNetWorthTransactionIntgService.processUserNetWorthTransaction(wrappedEvent.getData());

			log.info("Update User Net Worth Transaction Ended, Delay {} ms",
					Duration.between(now, Instant.now()).toMillis());
		}
		catch (Exception e) {
			log.error("Error occurred during Update User Net Worth Transaction. Error: {}", e.getMessage());
		}

	}

}
