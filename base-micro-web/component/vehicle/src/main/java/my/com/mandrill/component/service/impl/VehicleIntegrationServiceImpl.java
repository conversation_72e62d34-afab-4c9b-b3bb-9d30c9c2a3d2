package my.com.mandrill.component.service.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import my.com.mandrill.component.constant.NetWorthMainType;
import my.com.mandrill.component.constant.NetWorthSource;
import my.com.mandrill.component.constant.NetWorthType;
import my.com.mandrill.component.domain.Vehicle;
import my.com.mandrill.component.service.VehicleIntegrationService;
import my.com.mandrill.component.service.VehicleService;
import my.com.mandrill.utilities.feign.dto.request.UpdateUserNetWorthTransactionRequest;
import my.com.mandrill.utilities.feign.service.ProxyFeignClient;
import my.com.mandrill.utilities.general.constant.*;
import my.com.mandrill.utilities.general.dto.model.DashboardActivityMessage;
import my.com.mandrill.utilities.general.service.DashboardTriggerService;
import my.com.mandrill.utilities.general.service.KafkaSender;
import org.springframework.data.domain.Sort;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.Instant;
import java.util.List;
import java.util.function.Function;

@Slf4j
@Service
@RequiredArgsConstructor
public class VehicleIntegrationServiceImpl implements VehicleIntegrationService {

	private final VehicleService vehicleDataService;

	private final ProxyFeignClient proxyFeignClient;

	private final DashboardTriggerService dashboardTriggerService;

	private final KafkaTemplate<String, String> kafkaTemplate;

	private final KafkaSender kafkaSender;

	/**
	 * Because there are 2 conditions between the revamp and before the revamp, we will
	 * put the process before the revamp in postProcess, eg: creating insurance, loans and
	 * reminders.
	 * @param vehicle existing or new vehicle object
	 * @param postProcess process after saving vehicle
	 * @return vehicle persist
	 */
	@Override
	public Vehicle save(Vehicle vehicle, Function<Vehicle, Void> postProcess) {
		Vehicle persisted = vehicleDataService.save(vehicle);

		// due to disable open-in-view, need to remap transient field
		persisted.setVehicleType(vehicle.getVehicleType());
		persisted.setModel(vehicle.getModel());
		persisted.setModelGroup(vehicle.getModelGroup());
		persisted.setLoan(vehicle.getLoan());
		persisted.setIsReminder(vehicle.getIsReminder());
		persisted.setReminder(vehicle.getReminder());
		persisted.setInsurance(vehicle.getInsurance());

		postProcess.apply(persisted);

		return persisted;
	}

	/**
	 * Because there are 2 conditions between the revamp and before the revamp, we will
	 * put the process before the revamp in postProcess, eg: delete insurance, loans and
	 * reminders.
	 * @param id existing vehicle id
	 * @param postProcess process after delete vehicle
	 */
	@Override
	public void delete(String id, String userId, Function<String, Void> postProcess) {
		vehicleDataService.delete(id, userId);

		postProcess.apply(id);

		proxyFeignClient.getCommonFeignClient().deleteUserJourneyByEntityName(EntityName.VEHICLE, id);
		kafkaTemplate.send(KafkaTopic.VEHICLE_DELETION, id, userId);
	}

	@Override
	public void sendDashboardActivity(Instant createdDate) {
		DashboardActivityMessage dto = DashboardActivityMessage.builder().category(DashboardCategory.MODULE_RECORDS)
				.type(DashboardType.VEHICLES).value(1L).createdDate(createdDate).build();

		dashboardTriggerService.send(dto);
	}

	@Override
	public List<Vehicle> findDetachedInsurance(String userId) {
		List<String> linkedEntityIds = proxyFeignClient.getInsuranceFeignClient()
				.getLinkedEntityIds(InsuranceTypeEnum.VEHICLE);
		if (linkedEntityIds.isEmpty()) {
			return vehicleDataService.findAll(Sort.unsorted(), userId);
		}
		return vehicleDataService.findNotIn(userId, linkedEntityIds);
	}

	@Override
	public List<Vehicle> findDetachedLoan(String userId) {
		List<String> linkedEntityIds = proxyFeignClient.getBankFeignClient().getLinkedEntities(EntityName.VEHICLE);
		if (linkedEntityIds.isEmpty()) {
			return vehicleDataService.findAll(Sort.unsorted(), userId);
		}
		return vehicleDataService.findNotIn(userId, linkedEntityIds);
	}

	@Override
	public void publishNetWorthTransactionEvent(String userId, NetWorthMainType mainType, NetWorthType subType,
			NetWorthSource source) {

		BigDecimal totalVehicleValue = vehicleDataService.calculateNetWorthByUserId(userId).getAssets();

		UpdateUserNetWorthTransactionRequest request = UpdateUserNetWorthTransactionRequest.builder().userId(userId)
				.mainType(mainType).subType(subType).amount(totalVehicleValue).source(source).build();

		kafkaSender.safeSendWrapped(KafkaTopic.UPDATE_USER_NET_WORTH_TRANSACTION_TOPIC, userId, request);

	}

}
