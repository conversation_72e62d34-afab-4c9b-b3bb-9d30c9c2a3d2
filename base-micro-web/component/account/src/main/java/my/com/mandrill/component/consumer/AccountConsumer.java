package my.com.mandrill.component.consumer;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import my.com.mandrill.component.config.KafkaTopicConfig;
import my.com.mandrill.component.constant.RequestKeyStatus;
import my.com.mandrill.component.domain.AppUser;
import my.com.mandrill.component.domain.DashboardActivity;
import my.com.mandrill.component.domain.User;
import my.com.mandrill.component.domain.UserKeyRequest;
import my.com.mandrill.component.repository.jpa.UserKeyRequestRepository;
import my.com.mandrill.component.service.AppUserService;
import my.com.mandrill.component.service.DashboardActivityService;
import my.com.mandrill.component.service.UserService;
import my.com.mandrill.utilities.ciphers.AesCryptoUtil;
import my.com.mandrill.utilities.feign.dto.UpdateUserByFinologyDTO;
import my.com.mandrill.utilities.feign.dto.UpdateUserIdentityNumberByFinologyDTO;
import my.com.mandrill.utilities.general.constant.KafkaTopic;
import my.com.mandrill.utilities.general.constant.LoginTypeEnum;
import my.com.mandrill.utilities.general.constant.NricUpdateOrigin;
import my.com.mandrill.utilities.general.dto.model.AccountDeletionRequest;
import my.com.mandrill.utilities.general.dto.model.SchedulerMessaging;
import my.com.mandrill.utilities.general.exception.ExceptionPredicate;
import my.com.mandrill.utilities.general.util.JSONUtil;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.Instant;
import java.time.LocalDate;
import java.time.Period;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.List;
import java.util.Optional;

@Slf4j
@Service
@RequiredArgsConstructor
public class AccountConsumer {

	public static final String IC = "IC";

	public static final String PASSPORT = "PASSPORT";

	public static final String ARMY = "ARMY";

	public static final String PLAIN_DATE_FORMAT = "yyyy-MM-dd";

	private final ObjectMapper objectMapper;

	private final UserKeyRequestRepository userKeyRequestRepository;

	private final UserService userService;

	private final AppUserService appUserService;

	private final DashboardActivityService dashboardActivityService;

	private final KafkaTemplate<String, String> kafkaTemplate;

	private final JSONUtil jsonUtil;

	@Transactional
	@KafkaListener(topics = KafkaTopicConfig.KEY_EXPIRATION, groupId = KafkaTopicConfig.GROUP,
			id = KafkaTopicConfig.KEY_EXPIRATION)
	public void keyExpiration(String message) throws JsonProcessingException {
		SchedulerMessaging schedulerMessaging = objectMapper.readValue(message, SchedulerMessaging.class);
		log.info("Key Expiration Begin {}", message);
		List<UserKeyRequest> requests = userKeyRequestRepository
				.findByStatusAndExpiresAtBefore(RequestKeyStatus.PENDING, Instant.now());
		requests.forEach(userKeyRequest -> {
			userKeyRequest.setStatus(RequestKeyStatus.EXPIRED);
		});
		userKeyRequestRepository.saveAll(requests);
		log.info("Key Expiration Ended, Delay: {} ms",
				ChronoUnit.MILLIS.between(schedulerMessaging.getTimeJobTriggered(), Instant.now()));
	}

	@Transactional
	@KafkaListener(topics = KafkaTopicConfig.DASHBOARD_ACTIVITY, groupId = KafkaTopicConfig.GROUP,
			id = KafkaTopicConfig.DASHBOARD_ACTIVITY)
	public void dashboardActivity(String message) throws JsonProcessingException {
		log.info("start consume topic {} with message {}", KafkaTopicConfig.DASHBOARD_ACTIVITY, message);
		Instant start = Instant.now();
		DashboardActivity dashboardActivityMessage = objectMapper.readValue(message, DashboardActivity.class);
		this.dashboardActivityService.save(dashboardActivityMessage);
		log.info("end process consume topic {}, delay: {} ms", KafkaTopicConfig.DASHBOARD_ACTIVITY,
				ChronoUnit.MILLIS.between(start, Instant.now()));
	}

	@Transactional
	@KafkaListener(topics = KafkaTopicConfig.DELETE_ACCOUNT, groupId = KafkaTopicConfig.GROUP,
			id = KafkaTopicConfig.DELETE_ACCOUNT)
	public void deleteAccount(String message) throws JsonProcessingException {
		SchedulerMessaging schedulerMessaging = objectMapper.readValue(message, SchedulerMessaging.class);
		log.info("Delete Account Begin {}", message);
		List<User> userList = userService.findAllByAbleToLogicalDelete();
		userList.forEach(user -> {
			user.setDeleted(true);
			user.setFullName(null);
			user.setEmail(null);
			user.setPhoneNumber(null);
			user.setPhoneCountry(null);
			user.setNric(null, NricUpdateOrigin.ACCOUNT_DELETION);
			user.setAddress1(null);
			user.setAddress2(null);
			user.setAddress3(null);
		});
		userService.saveAll(userList);

		userList.forEach(user -> kafkaTemplate.send(KafkaTopic.RSM_ACCOUNT_DELETION_UPDATE_TOPIC, user.getId(),
				jsonUtil.convertToString(AccountDeletionRequest.builder().schedulerMessaging(schedulerMessaging)
						.userId(user.getId()).schedulerMessaging(schedulerMessaging).build())));
		log.info("Delete Account Ended, Delay: {} ms",
				ChronoUnit.MILLIS.between(schedulerMessaging.getTimeJobTriggered(), Instant.now()));
	}

	@Transactional
	@KafkaListener(topics = KafkaTopic.FINOLOGY_UPDATE_USER_TOPIC, groupId = KafkaTopicConfig.GROUP,
			id = KafkaTopic.FINOLOGY_UPDATE_USER_TOPIC)
	public void updateAccountByFinology(String message) {
		log.info("feature=finology-update-user|topic:{}|message:{}", KafkaTopic.FINOLOGY_UPDATE_USER_TOPIC, message);
		try {
			UpdateUserByFinologyDTO data = objectMapper.readValue(message, UpdateUserByFinologyDTO.class);
			Optional<AppUser> userOpt = appUserService.findById(data.getId());
			if (userOpt.isEmpty()) {
				return;
			}

			AppUser user = userOpt.get();
			// based on fsd, need to check whether particular field was empty or not. if
			// yes,
			// set with these value
			// 'If not, it will save back to user profiles accordingly.'
			if (user.getEmail() == null) {
				String encryptedEmail = AesCryptoUtil.basicEncrypt(data.getEmail());
				if (userService.existsByEmailAndLoginType(encryptedEmail, LoginTypeEnum.USER)) {
					log.info("email {} is has been used for another user", data.getEmail());
				}
				else {
					user.setEmail(data.getEmail());
				}
			}
			// there is 3 column for address, need to check it manually
			if (user.getAddress1() == null) {
				user.setAddress1(data.getAddress().get(0));
				user.setAddress2(data.getAddress().get(1));
				user.setAddress3(data.getAddress().get(2));
			}

			DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern(PLAIN_DATE_FORMAT)
					.withZone(ZoneId.systemDefault());
			LocalDate localDate = LocalDate.parse(data.getDob(), dateTimeFormatter);

			Period period = Period.between(localDate, LocalDate.now());
			user.setAge(period.getYears());
			appUserService.save(user);
		}
		catch (Exception e) {
			log.error("feature=finology-update-user|topic={}|message={}|error={}",
					KafkaTopic.FINOLOGY_UPDATE_USER_TOPIC, message, e.getMessage(), e);
			throw new RuntimeException("Error while updating finology-update-user", e);
		}
	}

	@Transactional
	@KafkaListener(topics = KafkaTopic.FINOLOGY_UPDATE_USER_IDENTITY_NUMBER_TOPIC, groupId = KafkaTopicConfig.GROUP,
			id = KafkaTopic.FINOLOGY_UPDATE_USER_IDENTITY_NUMBER_TOPIC)
	public void updateAccountNricByFinology(String message) {
		log.info("feature=finology-update-user|topic:{}|message:{}",
				KafkaTopic.FINOLOGY_UPDATE_USER_IDENTITY_NUMBER_TOPIC, message);
		try {
			UpdateUserIdentityNumberByFinologyDTO data = objectMapper.readValue(message,
					UpdateUserIdentityNumberByFinologyDTO.class);
			Optional<AppUser> userOpt = appUserService.findById(data.getId());
			if (userOpt.isEmpty()) {
				return;
			}

			AppUser user = userOpt.get();
			// based on fsd, need to check whether particular field was empty or not. if
			// yes,
			// set with these value
			// 'If not, it will save back to user profiles accordingly.'
			if (IC.equals(data.getIdentityType()) && user.getNric() == null) {
				user.setNric(data.getValue());
			}

			if (PASSPORT.equals(data.getIdentityType()) && user.getPassport() == null) {
				user.setPassport(data.getValue());
			}

			if (ARMY.equals(data.getIdentityType()) && user.getArmy() == null) {
				user.setArmy(data.getValue());
			}
			appUserService.save(user);
		}
		catch (Exception e) {
			log.error("feature=finology-update-user|topic={}|message={}|error={}",
					KafkaTopic.FINOLOGY_UPDATE_USER_IDENTITY_NUMBER_TOPIC, message, e.getMessage(), e);
			throw new RuntimeException("Error while updating finology-update-user", e);
		}
	}

}
