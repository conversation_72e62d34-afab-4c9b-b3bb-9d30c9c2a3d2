package my.com.mandrill.component.consumer;

import com.fasterxml.jackson.core.type.TypeReference;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import my.com.mandrill.component.config.KafkaTopicConfig;
import my.com.mandrill.component.service.AppUserService;
import my.com.mandrill.utilities.general.constant.KafkaTopic;
import my.com.mandrill.utilities.general.dto.request.PaymentInfoUpdateKafkaRequest;
import my.com.mandrill.utilities.general.util.JSONUtil;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@RequiredArgsConstructor
public class PaymentInfoConsumer {

	private final JSONUtil jsonUtil;

	private final AppUserService appUserService;

	@KafkaListener(topics = KafkaTopic.PAYMENT_INFO_UPDATE_TOPIC, groupId = KafkaTopicConfig.GROUP,
			id = KafkaTopic.PAYMENT_INFO_UPDATE_TOPIC)
	public void consume(String message) {
		log.info("feature=payment-info-consumer|topic={}|message={}", KafkaTopic.PAYMENT_INFO_UPDATE_TOPIC, message);
		try {
			PaymentInfoUpdateKafkaRequest kafkaRequest = jsonUtil.convertValueFromJson(message, new TypeReference<>() {
			});
			appUserService.updatePaymentStatus(kafkaRequest);
		}
		catch (Exception ex) {
			log.error("feature=payment-info-consumer|topic={}|message={}|error={}",
					KafkaTopic.PAYMENT_INFO_UPDATE_TOPIC, message, ex.getMessage(), ex);
		}
	}

}
