package my.com.mandrill.component.repository.jpa;

import my.com.mandrill.component.domain.AppUser;
import my.com.mandrill.utilities.general.constant.LoginTypeEnum;
import my.com.mandrill.utilities.general.constant.PaymentAccountStatus;
import org.springframework.data.jpa.repository.EntityGraph;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.time.Instant;
import java.util.Optional;

@Repository
public interface AppUserRepository extends JpaRepository<AppUser, String> {

	@EntityGraph(attributePaths = { "educationLevel", "employmentType", "occupationGroup", "businessNature",
			"authorities", "authorities.permissions", "institutions", "interests", "financialGoals", "incomes",
			"incomes.incomeType", "incomes.employmentType", "expenses", "expenses.expenseType", "segment",
			"nationality", "currency", "epfContributions" })
	Optional<AppUser> findByRefNoAndDeletedAndLoginType(String refNo, Boolean deleted, LoginTypeEnum loginType);

	@EntityGraph(attributePaths = { "educationLevel", "employmentType", "occupationGroup", "businessNature",
			"authorities", "authorities.permissions", "institutions", "interests", "financialGoals", "incomes",
			"incomes.incomeType", "incomes.employmentType", "expenses", "expenses.expenseType", "segment",
			"nationality", "currency", "epfContributions" })
	Optional<AppUser> findAuthByRefNoAndDeleted(String refNo, Boolean deleted);

	@EntityGraph(attributePaths = { "institutions", "authorities", "authorities.permissions" })
	Optional<AppUser> findUserAuthByPhoneNumberAndPhoneCountryAndDeletedAndLoginType(String phoneNumber,
			String phoneCountry, Boolean deleted, LoginTypeEnum loginType);

	@EntityGraph(attributePaths = { "institutions", "authorities", "authorities.permissions" })
	Optional<AppUser> findUserAuthByEmailAndDeletedAndLoginType(String email, Boolean deleted, LoginTypeEnum loginType);

	@EntityGraph(attributePaths = { "institutions", "authorities", "authorities.permissions" })
	Optional<AppUser> findUserAuthByEmailAndDeletedAndLoginTypeAndActive(String email, Boolean deleted,
			LoginTypeEnum loginType, Boolean active);

	@Modifying
	@Query("UPDATE AppUser au SET au.loginFailAttempt = ?1, au.lastModifiedDate = ?2 WHERE au.id = ?3")
	void updateFailAttempt(Integer loginFailAttempt, Instant modifiedDate, String id);

	@Modifying
	@Query("UPDATE AppUser au SET au.deletedDatetime = ?1, au.active=false, au.lastModifiedBy= ?3 WHERE au.id = ?2")
	void updateGracePeriod(Instant gracePeriodStart, String id, String executedBy);

	@Modifying
	@Query("UPDATE AppUser au SET au.loginFailAttempt = 0, au.loginFailDeviceLockLog = null WHERE au.id = ?1")
	void updateLoginFailAttempt(String id);

	Optional<AppUser> findByIdAndDeletedFalseAndDeletedDatetimeIsNull(String id);

	@Modifying
	@Query("update AppUser u set u.paymentInfoStatus = ?1, u.paymentInfoApprovedDate=?2 where u.id = ?3")
	void updatePaymentStatus(PaymentAccountStatus paymentInfoStatus, Instant paymentInfoApprovedDate, String id);

}
