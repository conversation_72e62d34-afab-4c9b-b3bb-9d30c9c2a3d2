package my.com.mandrill.component.service.impl;

import com.fasterxml.jackson.core.type.TypeReference;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import my.com.mandrill.component.config.MapStructConverter;
import my.com.mandrill.component.constant.AuthenticationProviderSource;
import my.com.mandrill.component.constant.RequestKeyStatus;
import my.com.mandrill.component.constant.RunningNumberModule;
import my.com.mandrill.component.constant.UsernameType;
import my.com.mandrill.component.domain.AppUser;
import my.com.mandrill.component.domain.PasswordTransaction;
import my.com.mandrill.component.domain.User;
import my.com.mandrill.component.domain.UserKeyRequest;
import my.com.mandrill.component.dto.model.CheckAccountProjection;
import my.com.mandrill.component.dto.model.UserDataForAiProjection;
import my.com.mandrill.component.dto.request.MobileUserPaginationRequest;
import my.com.mandrill.component.dto.request.UpdatePasswordResetEmailRequest;
import my.com.mandrill.component.dto.request.UpdateStatusInternalMobileRequest;
import my.com.mandrill.component.dto.response.*;
import my.com.mandrill.component.exception.ErrorCodeEnum;
import my.com.mandrill.component.repository.jpa.DeviceBindingRepository;
import my.com.mandrill.component.repository.jpa.UserKeyRequestRepository;
import my.com.mandrill.component.repository.jpa.UserRepository;
import my.com.mandrill.component.service.PasswordTransactionService;
import my.com.mandrill.component.service.UserService;
import my.com.mandrill.utilities.feign.dto.request.UpdateUserKafkaRequest;
import my.com.mandrill.utilities.feign.dto.response.UserFullNameResponse;
import my.com.mandrill.utilities.general.constant.*;
import my.com.mandrill.utilities.general.dto.request.PaymentInfoUpdateKafkaRequest;
import my.com.mandrill.utilities.general.exception.BusinessException;
import my.com.mandrill.utilities.general.exception.ExceptionPredicate;
import my.com.mandrill.utilities.general.service.RedisService;
import my.com.mandrill.utilities.general.util.CryptoUtil;
import my.com.mandrill.utilities.general.util.PhoneNumberUtil;
import my.com.mandrill.utilities.general.util.RunningNumberUtil;
import my.com.mandrill.utilities.general.util.SecurityUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Caching;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.*;
import java.util.List;
import java.util.Optional;
import java.util.Set;

@Slf4j
@Service
@Transactional(readOnly = true)
@RequiredArgsConstructor
public class UserServiceImpl implements UserService {

	private static final String DEVICE_LOGIN_LOCK_LOG = "%s trying to login with device: %s used by %s.<br/><br>Please reset device binding on %s.";

	private final UserRepository userRepository;

	private final UserKeyRequestRepository userKeyRequestRepository;

	private final RunningNumberUtil runningNumberUtil;

	private final PasswordEncoder passwordEncoder;

	private final PasswordTransactionService passwordTransactionService;

	private final DeviceBindingRepository deviceBindingRepository;

	private final RedisService redisService;

	@Override
	public User findByIdAndInstitutionsId(String id, String institutionId) {
		return userRepository.findByIdAndInstitutionsId(id, institutionId)
				.orElseThrow(ExceptionPredicate.userNotFound(id, LoginTypeEnum.ADMIN));
	}

	@Transactional
	@Caching(evict = { @CacheEvict(value = CacheKey.USER_BY_ID_CACHE, key = "#user.refNo"),
			@CacheEvict(value = CacheKey.APP_USER_BY_REF_NO, key = "#user.refNo"),
			@CacheEvict(value = CacheKey.APP_USER_BY_PHONE_NUMBER, key = "#user.phoneNumber",
					condition = "#user.phoneNumber != null"),
			@CacheEvict(value = CacheKey.APP_USER_BY_EMAIL, key = "#user.email", condition = "#user.email != null"), })
	@Override
	public User save(User user) {
		user.getAuthorities().forEach(auth -> user.getInstitutions().add(auth.getInstitution()));
		userRepository.saveAndFlush(user);
		return user;
	}

	@Transactional
	@Caching(evict = { @CacheEvict(value = CacheKey.USER_BY_ID_CACHE, key = "#user.refNo"),
			@CacheEvict(value = CacheKey.APP_USER_BY_REF_NO, key = "#user.refNo"),
			@CacheEvict(value = CacheKey.APP_USER_BY_PHONE_NUMBER, key = "#user.phoneNumber"),
			@CacheEvict(value = CacheKey.APP_USER_BY_EMAIL, key = "#user.email"), })
	@Override
	public int saveUserUpdateRequest(UpdateUserKafkaRequest request, User user) {
		return this.userRepository.updateUserFields(user.getId(), request);
	}

	@Transactional
	@Override
	public User adminSave(User user) {
		user.setLoginFailAttempt(0);
		user.setEmailVerified(true);
		user.setPhoneVerified(true);
		user.setPassword(passwordEncoder.encode(user.getPassword()));
		user.setProvider(AuthenticationProviderSource.DATABASE);
		user.setRefNo(runningNumberUtil.getLatestRunningNumber(RunningNumberModule.USER_REFERENCE.name(), true));
		user.setUsername(user.getRefNo());
		return save(user);
	}

	@Override
	public Page<User> findByInstitutionsIdAndLoginTypeAndFullName(String institutionId, LoginTypeEnum loginType,
			String fullName, Pageable pageable) {
		return userRepository.findByInstitutionsIdAndLoginTypeAndFullName(institutionId, loginType, fullName, pageable);
	}

	@Override
	public User findByIdAndInstitutionsIdAndLoginType(String username, String institutionId, LoginTypeEnum loginType) {
		return userRepository.findByIdAndInstitutionsIdAndLoginType(username, institutionId, loginType)
				.orElseThrow(ExceptionPredicate.userNotFound(username, loginType));
	}

	@Caching(evict = { @CacheEvict(value = CacheKey.USER_BY_ID_CACHE, key = "#user.refNo"),
			@CacheEvict(value = CacheKey.APP_USER_BY_REF_NO, key = "#user.refNo"),
			@CacheEvict(value = CacheKey.APP_USER_BY_PHONE_NUMBER, key = "#user.phoneNumber"),
			@CacheEvict(value = CacheKey.APP_USER_BY_EMAIL, key = "#user.email"), })
	public void delete(User user) {
		userRepository.delete(user);
	}

	@Override
	public User findByRefNo(String refNo) {
		return userRepository.findByRefNo(refNo).orElseThrow(ExceptionPredicate.userNotFoundByRefNo(refNo));
	}

	@Override
	public User findByUsernameInputIgnoreCaseAndLoginType(String username, LoginTypeEnum loginType, Boolean active) {
		switch (loginType) {
			case USER -> {
				User input = new User();
				input.setPhoneNumber(username.substring(3));
				return userRepository
						.findByPhoneCountryAndPhoneNumberAndLoginType(username.substring(0, 3),
								input.getPhoneNumberRaw(), loginType)
						.orElseThrow(ExceptionPredicate.userNotFound(username, loginType));
			}
			case ADMIN -> {
				User input = new User();
				input.setEmail(username.toLowerCase());
				return userRepository.findByEmailIgnoreCaseAndLoginType(input.getEmailRaw(), loginType)
						.orElseThrow(ExceptionPredicate.userNotFound(username, loginType));
			}
		}
		throw new BusinessException(ErrorCodeEnum.INVALID_LOGIN_TYPE);
	}

	@Override
	public User getCurrentUser() {
		return userRepository.findByUsernameIgnoreCaseAndActiveTrueAndDeletedFalse(SecurityUtil.currentUserLogin())
				.orElseThrow(ExceptionPredicate.userNotFound(SecurityUtil.currentUserLogin(), LoginTypeEnum.ADMIN));
	}

	@Override
	public User findByEmailAndLoginTypeAndActiveTrue(String email, LoginTypeEnum loginType) {
		User input = new User();
		input.setEmail(email.toLowerCase());
		return userRepository.findByEmailIgnoreCaseAndLoginTypeAndActiveTrue(input.getEmailRaw(), loginType)
				.orElseThrow(ExceptionPredicate.userNotFound(email, loginType));
	}

	@Override
	public User findByPhoneCountryAndPhoneNumberAndLoginTypeAndActiveTrue(String phoneCountry, String phoneNumber,
			LoginTypeEnum loginType) {
		User input = new User();
		input.setPhoneNumber(phoneNumber);
		return userRepository.findByPhoneCountryAndPhoneNumberAndLoginTypeAndActiveTrue(phoneCountry,
				input.getPhoneNumberRaw(), loginType)
				.orElseThrow(ExceptionPredicate.userNotFound(phoneCountry + phoneNumber, loginType));
	}

	@Transactional
	@Override
	public void completePasswordResetEmail(UpdatePasswordResetEmailRequest request) {
		UserKeyRequest keyInput = new UserKeyRequest();
		keyInput.setUsername(request.getEmail() == null ? null : request.getEmail().toLowerCase());
		keyInput.setKeyValue(request.getKey());
		Optional<UserKeyRequest> optionalKeyRequest = userKeyRequestRepository.findByUsernameAndKeyValueAndType(
				keyInput.getUsernameRaw(), keyInput.getKeyValueRaw(), RequestKeyType.PASSWORD_RESET);

		if (optionalKeyRequest.isPresent()) {
			UserKeyRequest keyRequest = optionalKeyRequest.get();
			if (keyRequest.getStatus().equals(RequestKeyStatus.EXPIRED)
					|| keyRequest.getStatus().equals(RequestKeyStatus.COMPLETED)) {
				throw new BusinessException(ErrorCodeEnum.INVALID_KEY);
			}

			User user = userRepository
					.findByEmailIgnoreCaseAndLoginTypeAndActiveTrue(keyInput.getUsernameRaw(), LoginTypeEnum.ADMIN)
					.orElseThrow(ExceptionPredicate.userNotFound(request.getEmail(), LoginTypeEnum.ADMIN));
			passwordTransactionService.validatePassword(user, request.getPassword());
			String currentPassword = user.getPassword();
			user.setPassword(passwordEncoder.encode(request.getPassword()));
			user.setLoginFailAttempt(0);
			userRepository.save(user);

			if (StringUtils.isNotBlank(currentPassword)) {
				PasswordTransaction passwordTransaction = new PasswordTransaction();
				passwordTransaction.setUser(user);
				passwordTransaction.setPasswordHash(currentPassword);
				passwordTransactionService.save(passwordTransaction);
			}

			keyRequest.setCompletionDate(Instant.now());
			keyRequest.setStatus(RequestKeyStatus.COMPLETED);
			userKeyRequestRepository.save(keyRequest);

			deleteAppUserCache(user);
		}
		else {
			throw new BusinessException(ErrorCodeEnum.INVALID_KEY);
		}
	}

	@Override
	@Transactional
	public void generateSecretKey() {
		User currentUser = getCurrentUser();

		if (!currentUser.getLoginType().equals(LoginTypeEnum.ADMIN)) {
			throw new AccessDeniedException("Not Allowed");
		}

		userRepository.findBySecretKeyNull().parallelStream().forEach(user -> {
			if (StringUtils.isBlank(user.getSecretKey())) {
				user.setSecretKey(CryptoUtil.generateSecretKey());
				userRepository.save(user);
			}

			deleteAppUserCache(user);
		});
	}

	@Override
	public Page<UserDetailResponse> findInternalMobileUser(Pageable pageable, MobileUserPaginationRequest request) {
		return userRepository
				.findInternalMobileUser(request.getFullName(), request.getEmail(), request.getPhoneCountry(),
						request.getPhoneNumber(), request.getRefNo(), request.getActive(),
						request.getPaymentAccountStatus(), request.getReferralCode(), request.getReferralCodeUsed(),
						request.getRsmRelationType(),
						request.getDateType() != null ? request.getDateType().getFieldName() : null,
						request.getStartDate(), request.getEndDate(), pageable)
				.map(MapStructConverter.MAPPER::toUserDetailResponse);
	}

	@Override
	@Transactional
	public void updateStatusInternalMobile(String id, UpdateStatusInternalMobileRequest request) {
		User user = userRepository.findById(id).orElseThrow(ExceptionPredicate.userNotFoundById(id));
		validateUserUpdate(user, request);
		updateUserStatus(user, request);
		userRepository.save(user);
		deleteAppUserCache(user);
	}

	private void validateUserUpdate(User user, UpdateStatusInternalMobileRequest request) {
		if (Boolean.TRUE.equals(user.getDeleted()) && isAlreadyDeletedMoreThan30Days(user)) {
			throw new BusinessException(ErrorCodeEnum.ACCOUNT_ALREADY_DELETED_MORE_THAN_30_DAYS);
		}

		if (Boolean.FALSE.equals(request.getIsDeleted()) && user.getDeletedDatetime() == null) {
			throw new BusinessException(ErrorCodeEnum.CANNOT_UPDATE_DELETE_STATUS_USER);
		}

		if (Boolean.TRUE.equals(request.getIsActive()) && user.getDeletedDatetime() != null) {
			throw new BusinessException(ErrorCodeEnum.CANNOT_UPDATE_STATUS_USER_TO_ACTIVE);
		}
	}

	private void updateUserStatus(User user, UpdateStatusInternalMobileRequest request) {
		Optional.ofNullable(request.getIsDeleted()).ifPresent(isDeleted -> {
			if (!isDeleted) {
				user.setDeletedDatetime(null);
				user.setActive(true);
				user.setDeleted(false);
			}
		});

		Optional.ofNullable(request.getIsActive()).ifPresent(user::setActive);
	}

	private boolean isAlreadyDeletedMoreThan30Days(User user) {
		return user.getDeletedDatetime() != null
				&& user.getDeletedDatetime().isBefore(Instant.now().minus(Duration.ofDays(30)));
	}

	@Override
	public User findInternalMobileUserById(String id) {
		return userRepository.findByIdAndLoginType(id, LoginTypeEnum.USER)
				.orElseThrow(ExceptionPredicate.userNotFoundById(id));
	}

	@Override
	public boolean existsByUsernameAndInstitutionsIdAndLoginType(String username, String curIns,
			LoginTypeEnum loginTypeEnum) {
		return userRepository.existsByUsernameAndInstitutionsIdAndLoginType(username, curIns, loginTypeEnum);
	}

	@Override
	public boolean existsByAuthoritiesId(String authorityId) {
		return userRepository.existsByAuthoritiesId(authorityId);
	}

	@Override
	public boolean existsBySegmentId(String segmentId) {
		return userRepository.existsBySegmentId(segmentId);
	}

	@Override
	public Optional<User> findByEmailIgnoreCaseAndLoginType(String emailRaw, LoginTypeEnum loginTypeEnum) {
		return userRepository.findByEmailIgnoreCaseAndLoginType(emailRaw, loginTypeEnum);
	}

	@Override
	public Optional<User> findByEmail(String email) {
		User user = new User();
		user.setEmail(email);
		return userRepository.findByEmailIgnoreCaseAndLoginType(user.getEmailRaw(), LoginTypeEnum.USER);
	}

	@Override
	public Optional<User> findByEmailIgnoreCaseAndLoginTypeAndActiveTrue(String emailRaw, LoginTypeEnum loginType) {
		return userRepository.findByEmailIgnoreCaseAndLoginTypeAndActiveTrue(emailRaw, loginType);
	}

	@Override
	public String findSecretKeyByRefNo(String refNo) {
		return userRepository.findSecretKeyByRefNo(refNo);
	}

	@Override
	public CurrentUserIdResponse findRefNoAndIdByRefNo(String refNo) {
		return userRepository.findRefNoAndIdByRefNo(refNo);
	}

	@Override
	public AccountResponse findActiveById(String id) {
		User user = userRepository.findByIdAndDeletedFalse(id).orElseThrow(ExceptionPredicate.userNotFoundByRefNo(id));
		AccountResponse response = MapStructConverter.MAPPER.toAccountResponse(user);
		response.setIncome(user.getIncomes().stream()
				.filter(income -> IncomeTypeEnum.SALARY.name().equals(income.getIncomeType().getCode())
						&& EmployeeTypeConstant.FULLTIME_EMPLOYEE_TYPE_ID.equals(income.getEmploymentType().getId()))
				.findFirst().orElse(null));
		return response;
	}

	@Override
	public Optional<User> findOptionalByPhoneCountryAndPhoneNumber(String phoneCountry, String phoneNumber) {
		User user = new User();
		user.setPhoneNumber(phoneNumber);
		return userRepository.findByPhoneCountryAndPhoneNumberAndLoginType(phoneCountry, user.getPhoneNumberRaw(),
				LoginTypeEnum.USER);
	}

	@Override
	public List<User> findAllByAbleToLogicalDelete() {
		return userRepository.findAllByAbleToLogicalDelete();
	}

	@Override
	public Optional<User> findById(String id) {
		return userRepository.findById(id);
	}

	@Override
	public boolean existsByEmailAndLoginType(String encryptedEmail, LoginTypeEnum loginTypeEnum) {
		return userRepository.existsByEmailAndLoginType(encryptedEmail, loginTypeEnum);
	}

	@Override
	@Transactional
	public void saveAll(List<User> users) {
		userRepository.saveAll(users);
	}

	@Override
	public List<User> findByDobNotNull() {
		return userRepository.findByDobNotNull();
	}

	@Override
	public boolean existsByEmploymentTypeId(String employmentTypeId) {
		return userRepository.existsByEmploymentTypeId(employmentTypeId);
	}

	@Override
	public long countByDeletedAndLoginType(Instant startDateInstant, Instant endDateInstant,
			LoginTypeEnum loginTypeEnum) {
		return userRepository.countByDeletedAndLoginType(startDateInstant, endDateInstant, loginTypeEnum);
	}

	@Override
	public Page<String> getUserActiveAndUndeleted(Pageable pageable) {
		return userRepository.findByActiveAndDeleted(true, false, pageable);
	}

	@Override
	public List<String> findAllUserIdsByCreatedDate(Instant dateStart, Instant dateEnd) {
		return userRepository.findAllUserIdsByCreatedDate(dateStart, dateEnd);
	}

	@Override
	public Page<UserListResponse> findRefNoByCreatedDate(Pageable pageable, LocalDate dateFrom, LocalDate dateTo,
			ZoneId zoneId) {
		final Instant dateStart = dateFrom.atStartOfDay(zoneId).toInstant();
		final Instant dateEnd = dateTo.atTime(LocalTime.MAX).atZone(zoneId).toInstant();

		return userRepository.getUserListByCreatedDate(dateStart, dateEnd, pageable)
				.map(user -> mapToUserListResponse(user, zoneId));
	}

	private UserListResponse mapToUserListResponse(User user, ZoneId zoneId) {
		UserListResponse response = new UserListResponse();
		response.setCreatedDate(user.getCreatedDate().atZone(zoneId).toLocalDate());
		response.setRefNo(user.getRefNo());
		response.setCreatedDateTime(user.getCreatedDate());
		return response;
	}

	@Override
	@Transactional
	public int setLoginFailAttempt(Integer failAttempt, String userId) {
		return userRepository.setLoginFailAttempt(failAttempt, userId);
	}

	@Override
	public List<String> findAllUserIdsByCreatedDateLessThan(Instant createdDate) {
		return userRepository.findAllUserIdsByCreatedDateLessThan(createdDate);
	}

	@Override
	public List<UserFullNameResponse> findAllUserFullNames() {
		return userRepository.findAllUserFullNames();
	}

	@Override
	public List<InternalUserMobileResponse> findAllByUserIds(List<String> userIds) {
		return userRepository.findAllByUserIds(userIds);
	}

	@Override
	public List<UserFullNameResponse> findUserFullNamesByUserIds(List<String> userIds, boolean includeDeleted) {
		return userRepository.findUserFullNamesByUserIds(userIds, includeDeleted);
	}

	@Override
	public List<UserFullNameResponse> findUserFullNamesByUserRefNos(List<String> userRefNos) {
		return userRepository.findUserFullNamesByUserRefNos(userRefNos);
	}

	@Override
	@Transactional
	public void setLoginFailDeviceLockLog(String userId, String deviceId, Instant logoutDatetime) {
		Set<String> users = deviceBindingRepository.findUserByDeviceIdAndLogoutDatetimeAfter(deviceId, logoutDatetime);
		String usersConcat = String.join(",", users);
		String userRefNo = userRepository.findRefNoById(userId);

		String message = DEVICE_LOGIN_LOCK_LOG.formatted(userRefNo, deviceId, usersConcat, usersConcat);
		int effected = userRepository.setLoginFailDeviceLockLog(message, userId);
		log.info("SET LOGIN FAIL DEVICE LOCK USER-EFFECTED: {}", effected);
	}

	@Override
	public Optional<CheckAccountProjection> checkAccountByUsername(String username, UsernameType usernameType) {
		return switch (usernameType) {
			case PHONE_NUMBER -> {
				PhoneNumberUtil.ExtractedPhoneNumber phoneNumber = PhoneNumberUtil
						.getPhoneNumberWithNoCountryCode(username);
				User user = new User();
				user.setPhoneNumber(phoneNumber.getPhoneNumber());
				yield userRepository.checkByPhoneNumber(phoneNumber.getPhoneCountry(), user.getPhoneNumberRaw(),
						LoginTypeEnum.USER);
			}
			case EMAIL -> {
				User user = new User();
				user.setEmail(username);
				yield userRepository.checkByEmail(user.getEmailRaw(), LoginTypeEnum.USER);
			}
		};
	}

	@Transactional
	@Override
	public void updateReferralTnc(String referralCodeTnc) {
		String userId = SecurityUtil.currentUserId();
		String userLogin = SecurityUtil.currentUserLogin();

		userRepository.updateTncVersion(referralCodeTnc, Instant.now(), SecurityUtil.currentUserLogin(), userId);
		Optional<AppUser> optionalUser = redisService.getFromValue(
				CacheKey.CACHE_FORMAT.formatted(CacheKey.APP_USER_BY_REF_NO, userLogin), new TypeReference<>() {
				});

		if (optionalUser.isPresent()) {
			AppUser appUser = optionalUser.get();
			appUser.setReferralCodeTermConditionVersion(referralCodeTnc);

			updateAppUserCache(CacheKey.APP_USER_BY_REF_NO, appUser.getUsername(), appUser);
			updateAppUserCache(CacheKey.APP_USER_BY_EMAIL, appUser.getEmail(), appUser);
			updateAppUserCache(CacheKey.APP_USER_BY_PHONE_NUMBER, appUser.getPhoneNumber(), appUser);
		}
	}

	private void updateAppUserCache(String cacheKey, String fieldKey, AppUser updatedUser) {
		if (fieldKey == null)
			return;

		Optional<AppUser> cachedUser = redisService.getFromValue(CacheKey.CACHE_FORMAT.formatted(cacheKey, fieldKey),
				new TypeReference<>() {
				});
		Duration cacheDuration = Duration.ofMinutes(15);
		cachedUser.ifPresent(user -> {
			user.setReferralCodeTermConditionVersion(updatedUser.getReferralCodeTermConditionVersion());
			redisService.putToValue(CacheKey.CACHE_FORMAT.formatted(cacheKey, fieldKey), user, cacheDuration);
		});
	}

	private void deleteAppUserCache(User user) {
		redisService.deleteFromValue(CacheKey.CACHE_FORMAT.formatted(CacheKey.USER_BY_ID_CACHE, user.getId()));
		redisService.deleteFromValue(CacheKey.CACHE_FORMAT.formatted(CacheKey.APP_USER_BY_REF_NO, user.getRefNo()));
		if (StringUtils.isNotBlank(user.getPhoneNumber())) {
			redisService.deleteFromValue(
					CacheKey.CACHE_FORMAT.formatted(CacheKey.APP_USER_BY_PHONE_NUMBER, user.getPhoneNumber()));
		}
		if (StringUtils.isNotBlank(user.getEmail())) {
			redisService.deleteFromValue(CacheKey.CACHE_FORMAT.formatted(CacheKey.APP_USER_BY_EMAIL, user.getEmail()));
		}
	}

	@Override
	public UserDataForAiProjection findCurrentUserDataForAiById(String userId) {
		return userRepository.findCurrentUserForAiById(userId);
	}

	@Override
	public Optional<User> findByPhoneCountryAndPhoneNumber(String phoneCountry, String phoneNumber) {
		return userRepository.findByPhoneNumberAndPhoneCountry(phoneNumber, phoneCountry);
	}

	@Override
	public boolean existsByPhoneCountryAndPhoneNumber(String phoneCountry, String phoneNumber) {
		return userRepository.existsByPhoneCountryAndPhoneNumberAndDeletedFalse(phoneCountry, phoneNumber);
	}

	@Override
	public List<String> findUserIdsByPermission(String permission) {
		return userRepository.findUserIdsByPermission(permission);
	}

}
