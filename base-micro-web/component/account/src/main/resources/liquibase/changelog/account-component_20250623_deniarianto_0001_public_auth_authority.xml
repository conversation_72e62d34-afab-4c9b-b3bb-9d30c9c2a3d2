<?xml version="1.0" encoding="utf-8"?>
<databaseChangeLog
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog https://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.11.xsd">
    <changeSet id="account-component_20250623_deniarianto_0001" author="deniarianto">
        <createTable tableName="public_authentication_authority">
            <column name="authority_id" type="VARCHAR(36)">
                <constraints nullable="false" primaryKey="true" primaryKeyName="pk_public_auth_authority"/>
            </column>
            <column name="public_authentication_id" type="VARCHAR(36)">
                <constraints nullable="false" primaryKey="true" primaryKeyName="pk_public_auth_authority"/>
            </column>
        </createTable>
        <addForeignKeyConstraint baseColumnNames="authority_id" baseTableName="public_authentication_authority"
                                 constraintName="fk_pubauth_on_authority" referencedColumnNames="id"
                                 referencedTableName="app_authority"/>
        <addForeignKeyConstraint baseColumnNames="public_authentication_id" baseTableName="public_authentication_authority"
                                 constraintName="fk_pubauth_on_user" referencedColumnNames="id"
                                 referencedTableName="public_authentication"/>

        <loadData encoding="UTF-8"
                  file="liquibase/dataset/authorities/authorities_deniarianto_20250623.csv"
                  separator=";"
                  tableName="app_authority"/>

        <loadData encoding="UTF-8"
                  file="liquibase/dataset/permission/permission_mxbiz_deniarianto_20250623.csv"
                  separator=";"
                  tableName="app_permission"/>

        <loadData encoding="UTF-8"
                  file="liquibase/dataset/authority_permission/authority_permission_public_auth_deniarianto_20250623.csv"
                  separator=";"
                  tableName="app_authority_permission"/>

        <loadData encoding="UTF-8"
                  file="liquibase/dataset/public_user_authority/public_auth_authorities_deniarianto_20250623.csv"
                  separator=";"
                  tableName="public_authentication_authority"/>
    </changeSet>
</databaseChangeLog>




