package my.com.mandrill.utilities.feign.dto.response;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Builder;
import lombok.Data;

import java.io.Serializable;

@Data
@Builder
@JsonInclude(JsonInclude.Include.NON_NULL)
public class PartnerResponseDTO implements Serializable {

	private String code;

	private String name;

	private boolean active;

	@JsonProperty("isPartner")
	private boolean isPartner;

	private String issuerCode;

}
