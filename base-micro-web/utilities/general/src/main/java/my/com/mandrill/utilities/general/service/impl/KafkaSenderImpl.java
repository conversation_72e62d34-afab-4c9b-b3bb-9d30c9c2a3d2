package my.com.mandrill.utilities.general.service.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import my.com.mandrill.utilities.general.dto.kafka.KafkaEventWrapper;
import my.com.mandrill.utilities.general.service.KafkaEventWrapperService;
import my.com.mandrill.utilities.general.service.KafkaSender;
import my.com.mandrill.utilities.general.util.JSONUtil;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@RequiredArgsConstructor
public class KafkaSenderImpl implements KafkaSender {

	private final KafkaTemplate<String, String> kafkaTemplate;

	private final JSONUtil jsonUtil;

	private final KafkaEventWrapperService kafkaEventWrapperService;

	@Override
	public <T> void safeSend(String topic, String key, T payload) {
		try {
			String message = jsonUtil.convertToString(payload);
			kafkaTemplate.send(topic, key, message).whenComplete((result, ex) -> {
				getResultLog(topic, key, ex);
			});
		}
		catch (Exception ex) {
			log.error("feature=kafka-sender, result=error when serializing or sending, topic={}, key={}, Error={}",
					topic, key, ex.getMessage(), ex);
			throw ex;
		}
	}

	@Override
	public <T> void send(String topic, String key, T payload) {
		try {
			String message = jsonUtil.convertToString(payload);
			kafkaTemplate.send(topic, key, message).whenComplete((result, ex) -> {
				getResultLog(topic, key, ex);
			});
		}
		catch (Exception ex) {
			log.error("feature=kafka-sender, result=error when serializing or sending, topic={}, key={}, Error={}",
					topic, key, ex.getMessage(), ex);
		}
	}

	@Override
	public <T> void safeSendWrapped(String topic, String key, T payload) {

		KafkaEventWrapper<T> wrappedEvent = kafkaEventWrapperService.wrapEvent(payload);

		safeSend(topic, key, wrappedEvent);

	}

	private void getResultLog(String topic, String key, Throwable ex) {
		if (ex == null) {
			log.info("feature=kafka-sender, result=success, topic={}, key={}", topic, key);
		}
		else {
			log.error("feature=kafka-sender, result=failed, topic={}, key={}, Error={}", topic, key, ex.getMessage(),
					ex);
		}
	}

}
