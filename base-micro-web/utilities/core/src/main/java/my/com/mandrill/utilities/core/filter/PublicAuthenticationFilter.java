package my.com.mandrill.utilities.core.filter;

import com.fasterxml.jackson.core.type.TypeReference;
import jakarta.servlet.FilterChain;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import my.com.mandrill.utilities.core.dto.model.Request;
import my.com.mandrill.utilities.feign.client.AccountFeignClient;
import my.com.mandrill.utilities.feign.dto.PublicAuthenticationDTO;
import my.com.mandrill.utilities.general.constant.CacheKey;
import my.com.mandrill.utilities.general.constant.ErrorCodeGlobalEnum;
import my.com.mandrill.utilities.general.dto.request.HashRequest;
import my.com.mandrill.utilities.general.exception.BusinessException;
import my.com.mandrill.utilities.general.service.RedisService;
import my.com.mandrill.utilities.general.util.HashUtil;
import my.com.mandrill.utilities.general.util.RequestUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.lang.NonNull;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.util.AntPathMatcher;
import org.springframework.web.filter.OncePerRequestFilter;
import org.springframework.web.servlet.HandlerExceptionResolver;

import java.time.Duration;
import java.time.Instant;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

@Slf4j
public class PublicAuthenticationFilter extends OncePerRequestFilter {

	private final AccountFeignClient accountFeignClient;

	private final AntPathMatcher antPathMatcher = new AntPathMatcher();

	private final HandlerExceptionResolver handlerExceptionResolver;

	private final List<Request> filteredRequests;

	private final RedisService redisService;

	public PublicAuthenticationFilter(AccountFeignClient accountFeignClient,
			HandlerExceptionResolver handlerExceptionResolver, List<Request> filteredRequests,
			RedisService redisService) {
		this.accountFeignClient = accountFeignClient;
		this.handlerExceptionResolver = handlerExceptionResolver;
		this.filteredRequests = filteredRequests;
		this.redisService = redisService;
		log.info("PublicAuthenticationFilter will filter: {}", filteredRequests);
	}

	@Override
	protected void doFilterInternal(@NonNull HttpServletRequest request, @NonNull HttpServletResponse response,
			@NonNull FilterChain filterChain) {

		try {
			String hash = request.getHeader(RequestUtil.HASH);
			String identifier = request.getHeader(RequestUtil.IDENTIFIER);
			// principal should be the user's refNo
			String principalHeader = request.getHeader(RequestUtil.PRINCIPAL);
			Instant timestamp = RequestUtil.parseTimestampOrNull(request);

			if (StringUtils.isBlank(hash) || StringUtils.isBlank(identifier) || timestamp == null) {
				throw new BadCredentialsException("Missing required headers");
			}

			String principal = Objects.nonNull(principalHeader) ? principalHeader : identifier;

			PublicAuthenticationDTO publicAuthenticationDTO = this
					.validateHash(new HashRequest(identifier, hash, timestamp), request);
			List<SimpleGrantedAuthority> grantedAuthorities = Optional
					.ofNullable(publicAuthenticationDTO.getPermissions())
					.map(v -> v.stream().map(SimpleGrantedAuthority::new).toList()).orElse(new ArrayList<>());

			UsernamePasswordAuthenticationToken authentication = new UsernamePasswordAuthenticationToken(principal,
					hash, grantedAuthorities);
			SecurityContextHolder.getContext().setAuthentication(authentication);

			filterChain.doFilter(request, response);
		}
		catch (Exception e) {
			log.error("Exception during Authentication: {}", e.getMessage());
			handlerExceptionResolver.resolveException(request, response, null, e);
		}
	}

	@Override
	protected boolean shouldNotFilter(@NonNull HttpServletRequest request) {
		return filteredRequests.stream().noneMatch(s -> antPathMatcher.match(s.getPath(), request.getRequestURI())
				&& request.getMethod().equals(s.getMethod().name()));
	}

	private PublicAuthenticationDTO validateHash(HashRequest requestBody, HttpServletRequest request) {
		Optional<PublicAuthenticationDTO> publicAuthenticationOpt = getApiKey(requestBody.getIdentifier());
		if (publicAuthenticationOpt.isPresent()) {
			Instant now = Instant.now();
			if (Duration.between(requestBody.getTimestamp(), now).getSeconds() >= 60) {
				log.warn("error time : {}, {}, {}", requestBody.getTimestamp(), now,
						Duration.between(requestBody.getTimestamp(), now).getSeconds());
				throw new BusinessException(ErrorCodeGlobalEnum.WRONG_CREDENTIALS);
			}
			String hash = HashUtil.sha512Hex(publicAuthenticationOpt.get().getApiKey(), requestBody.getIdentifier(),
					requestBody.getTimestamp().toString());
			if (!hash.equals(requestBody.getHash())) {
				log.warn("Attempting to authenticate hash failed from ipAddress: {}. Reason: Wrong hash output",
						RequestUtil.getIpAddress(request));
				throw new BusinessException(ErrorCodeGlobalEnum.WRONG_CREDENTIALS);
			}
			return publicAuthenticationOpt.get();
		}
		else {
			log.warn("Attempting to authenticate hash failed from ipAddress: {}. Reason: Identifier not found",
					RequestUtil.getIpAddress(request));
			throw new BusinessException(ErrorCodeGlobalEnum.WRONG_CREDENTIALS);
		}
	}

	private Optional<PublicAuthenticationDTO> getApiKey(String identifier) {
		String cacheKey = CacheKey.PUBLIC_AUTHENTICATION_MODULE_CACHE_KEY.formatted(identifier);
		return redisService.getFromValue(cacheKey, new TypeReference<PublicAuthenticationDTO>() {
		}).or(() -> {
			PublicAuthenticationDTO publicAuth = accountFeignClient.getPublicAuthentication(identifier);
			redisService.putToValue(cacheKey, publicAuth, Duration.ofMinutes(5));
			return Optional.of(publicAuth);
		});
	}

}
